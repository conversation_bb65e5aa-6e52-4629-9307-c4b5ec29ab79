'use server';

import { GymPackages } from '@/types/database/tables';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';
import { createTrainerAction } from '../../core/trainer-action';

// Package creation schema
const createPackageSchema = z.object({
  gymId: z.uuid('Geçersiz salon ID'),
  name: z.string().min(1, 'Paket adı gereklidir'),
  package_type: z.enum(['appointment_standard', 'appointment_vip', 'daily']),
  duration_days: z.number().int().positive().optional(),
  price: z.number().positive("Fiyat 0'dan büyük olmalıdır"),
  description: z.string().optional(),
  max_participants: z.number().int().positive().optional(),
  session_count: z.number().int().positive().optional(),
  session_duration_minutes: z.number().int().positive().optional(),
  is_active: z.boolean(),
});

type CreatePackageInput = z.infer<typeof createPackageSchema>;

/**
 * Trainer tarafından salon paketi oluşturur
 */
export async function createGymPackageAsTrainer(
  data: CreatePackageInput
): Promise<GymPackages> {
  const result = await createTrainerAction<GymPackages>(
    data.gymId,
    async (_, supabase, _userId, gymId, _gymTrainerId) => {
      // Paketi oluştur
      const { data: newPackage, error } = await supabase
        .from('gym_packages')
        .insert({
          name: data.name,
          package_type: data.package_type,
          duration_days: data.duration_days || null,
          price: data.price,
          description: data.description || null,
          max_participants: data.max_participants,
          session_count: data.session_count,
          session_duration_minutes: data.session_duration_minutes,
          is_active: data.is_active,
          gym_id: gymId,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Paket oluşturulurken hata: ${error.message}`);
      }

      // Cache'i yenile
      revalidatePath(`/dashboard/trainer/gym/${gymId}/packages`);

      return newPackage as GymPackages;
    },
    {
      requiredPermission: {
        category: 'packages',
        action: 'create',
      },
    }
  );

  if (!result.success) {
    throw new Error(result.error || 'Paket oluşturulamadı');
  }

  return result.data!;
}

// Package update schema
const updatePackageSchema = z.object({
  gymId: z.uuid('Geçersiz salon ID'),
  packageId: z.uuid(),
  name: z.string().min(1, 'Paket adı gereklidir').optional(),
  package_type: z.enum(['appointment_standard', 'appointment_vip', 'daily']).optional(),
  duration_days: z.number().int().positive().optional(),
  price: z.number().positive("Fiyat 0'dan büyük olmalıdır").optional(),
  description: z.string().optional(),
  max_participants: z.number().int().positive().optional(),
  session_count: z.number().int().positive().optional(),
  session_duration_minutes: z.number().int().positive().optional(),
  is_active: z.boolean().optional(),
});

type UpdatePackageInput = z.infer<typeof updatePackageSchema>;

/**
 * Trainer tarafından salon paketini günceller
 */
export async function updateGymPackageAsTrainer(
  data: UpdatePackageInput
): Promise<GymPackages> {
  const result = await createTrainerAction<GymPackages>(
    data.gymId,
    async (_, supabase, _userId, gymId, _gymTrainerId) => {
      const { packageId, gymId: dataGymId, ...updateData } = data;

      // Paketi güncelle
      const { data: updatedPackage, error } = await supabase
        .from('gym_packages')
        .update({
          ...updateData,
          description: updateData.description || null,
          duration_days: updateData.duration_days || null,
        })
        .eq('id', packageId)
        .eq('gym_id', gymId) // Güvenlik için gym_id kontrolü
        .select()
        .single();

      if (error) {
        throw new Error(`Paket güncellenirken hata: ${error.message}`);
      }

      // Cache'i yenile
      revalidatePath(`/dashboard/trainer/gym/${gymId}/packages`);

      return updatedPackage as GymPackages;
    },
    {
      requiredPermission: {
        category: 'packages',
        action: 'update',
      },
    }
  );

  if (!result.success) {
    throw new Error(result.error || 'Paket güncellenemedi');
  }

  return result.data!;
}
