'use server';

import { createTrainerAction } from '../../core/trainer-action';
import { ApiResponse } from '@/types/global/api';
import { Gyms, GymMemberships, Profiles } from '@/types/database/tables';

export interface TrainerGymStats {
  memberCount: number;
  trainerCount: number;
  packageCount: number;
}

// Üyelik ve üye bilgilerini içeren tip
type MembershipWithMember = GymMemberships & {
  member: Profiles | null;
};

/**
 * Get gym statistics for trainer dashboard
 * Following Clean Code principles - focused responsibility, clear validation
 */
export async function getTrainerGymStats(
  gymId: string
): Promise<ApiResponse<TrainerGymStats>> {
  return await createTrainerAction<TrainerGymStats>(
    gymId,
    async (_, supabase) => {
      try {
        // Toplam üye sayısı - RLS politikası sayesinde trainer kendi gym'indeki üyeleri görebilir
        const { count: memberCount } = await supabase
          .from('gym_memberships')
          .select('*', { count: 'exact', head: true })
          .eq('gym_id', gymId)
          .eq('status', 'active');

        // Toplam trainer sayısı - RLS politikası sayesinde trainer kendi gym'indeki trainer'ları görebilir
        const { count: trainerCount } = await supabase
          .from('gym_trainers')
          .select('*', { count: 'exact', head: true })
          .eq('gym_id', gymId)
          .eq('status', 'active');

        // Toplam paket sayısı - RLS politikası sayesinde trainer kendi gym'indeki paketleri görebilir
        const { count: packageCount } = await supabase
          .from('gym_packages')
          .select('*', { count: 'exact', head: true })
          .eq('gym_id', gymId)
          .eq('status', 'active');

        return {
          memberCount: memberCount || 0,
          trainerCount: trainerCount || 0,
          packageCount: packageCount || 0,
        };
      } catch (error) {
        console.error('Gym stats fetch error:', error);
        throw new Error('İstatistikler alınırken hata oluştu');
      }
    }
  );
}

/**
 * Get gym information for trainer (with trainer access validation)
 */
export async function getGymByIdForTrainer(
  gymId: string
): Promise<ApiResponse<Gyms>> {
  return await createTrainerAction<Gyms>(gymId, async (_, supabase) => {
    // Get gym information - RLS politikası sayesinde normal client ile erişebiliriz
    const { data: gym, error: gymError } = await supabase
      .from('gyms')
      .select('*')
      .eq('id', gymId)
      .eq('status', 'active')
      .single();

    if (gymError || !gym) {
      throw new Error('Salon bulunamadı veya aktif değil');
    }

    return gym;
  });
}

/**
 * Get gym members for trainer (with trainer access validation)
 */
export async function getGymMembersForTrainer(
  gymId: string
): Promise<ApiResponse<MembershipWithMember[]>> {
  return await createTrainerAction<MembershipWithMember[]>(
    gymId,
    async (_, supabase) => {
      // Get gym members - RLS politikası sayesinde trainer kendi gym'indeki üyeleri görebilir
      const { data: memberships, error: membershipsError } = await supabase
        .from('gym_memberships')
        .select(
          `
            *,
            member:profiles!gym_memberships_profile_id_fkey
(*)
          `
        )
        .eq('gym_id', gymId)
        .order('created_at', { ascending: false });

      if (membershipsError) {
        throw new Error(
          `Üyeler getirilirken hata: ${membershipsError.message}`
        );
      }

      return memberships || [];
    },
    {
      requiredPermission: { category: 'members', action: 'read' },
    }
  );
}
