'use server';

import { TrainerPermissions } from './server-auth';
import { getTrainerPermissions } from '@/lib/actions/dashboard/company/trainer-permissions';
import { createTrainerAction } from '@/lib/actions/core/trainer-action';
import { redirect } from 'next/navigation';

/**
 * Trainer'ın belirli bir gym'deki permissions'larını getirir
 * createTrainerAction kullanarak otomatik gym access kontrolü yapar
 */
export async function getTrainerGymPermissions(gymId: string): Promise<{
  permissions: TrainerPermissions;
  gymAssignment: { id: string };
}> {
  const result = await createTrainerAction<{
    permissions: TrainerPermissions;
    gymAssignment: { id: string };
  }>(gymId, async (_, _supabase, userId, gymId, gymTrainerId) => {
    // Permissions'ları getir
    const permissionsResult = await getTrainerPermissions(gymId, userId);

    if (!permissionsResult.success || !permissionsResult.data) {
      throw new Error('Yetki bilgileri alınamadı');
    }

    return {
      permissions: permissionsResult.data,
      gymAssignment: { id: gymTrainerId },
    };
  });

  if (!result.success) {
    throw new Error(result.error || 'Yetki bilgileri alınamadı');
  }

  return result.data!;
}

/**
 * Trainer'ın belirli bir permission'a sahip olup olmadığını kontrol eder
 * Eğer yoksa error sayfasına yönlendirir
 */
export async function requireTrainerPermission(
  gymId: string,
  category: keyof TrainerPermissions,
  action: string,
  errorMessage?: string
): Promise<{
  permissions: TrainerPermissions;
  gymAssignment: { id: string };
}> {
  const { permissions, gymAssignment } = await getTrainerGymPermissions(gymId);

  // Permission kontrolü
  const categoryPermissions = permissions[category] as any;
  const hasPermission =
    categoryPermissions && categoryPermissions[action] === true;

  if (!hasPermission) {
    const defaultMessage = `Bu işlem için yetkiniz bulunmuyor`;
    const message = errorMessage || defaultMessage;

    redirect(
      `/error?type=permission_denied&message=${encodeURIComponent(message)}`
    );
  }

  return { permissions, gymAssignment };
}

/**
 * Sadece belirli bir kategori yetkisini çeken optimized fonksiyon
 * Tüm yetkileri almak yerine sadece ihtiyaç olan kategoriyi getirir
 */
export async function getTrainerCategoryPermissions<
  T extends keyof TrainerPermissions,
>(
  gymId: string,
  category: T
): Promise<{
  permissions: TrainerPermissions[T];
  gymAssignment: { id: string };
}> {
  const result = await createTrainerAction<{
    permissions: TrainerPermissions[T];
    gymAssignment: { id: string };
  }>(gymId, async (_, _supabase, userId, gymId, gymTrainerId) => {
    // Tüm permissions'ları al (RPC fonksiyonu tek seferde tüm yetkileri döndürür)
    const permissionsResult = await getTrainerPermissions(gymId, userId);

    if (!permissionsResult.success || !permissionsResult.data) {
      throw new Error('Yetki bilgileri alınamadı');
    }

    // Sadece istenen kategoriyi döndür
    const categoryPermissions = permissionsResult.data[category];

    return {
      permissions: categoryPermissions,
      gymAssignment: { id: gymTrainerId },
    };
  });

  if (!result.success) {
    throw new Error(result.error || 'Yetki bilgileri alınamadı');
  }

  return result.data!;
}

/**
 * Permission kontrolü yapan component wrapper
 * Eğer permission yoksa hata mesajı gösterir
 */
export interface PermissionGuardProps {
  permissions: TrainerPermissions;
  category: keyof TrainerPermissions;
  action: string;
  errorMessage?: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}
