'use client';

import { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Search, Loader2, UserPlus, Check, Users, Mail, QrCode } from 'lucide-react';
import { toast } from 'sonner';
import { useDebounce } from '@/hooks/use-debounce';
import { searchUsers, findMemberByCode, MemberSearchResult } from '@/lib/actions/gym_invitations/user-search-service';
import { sendGymMemberInvite } from '@/lib/actions/gym_invitations/invitation-actions';
import { UserSearchResult } from '@/lib/actions/gym_invitations/invitation-types';

interface InviteMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  gymId: string;
  onMemberInvited?: () => void;
}

export function InviteMemberDialog({
  isOpen,
  onClose,
  gymId,
  onMemberInvited,
}: InviteMemberDialogProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [memberCode, setMemberCode] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [foundMember, setFoundMember] = useState<MemberSearchResult | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [isSearchingMember, setIsSearchingMember] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [invitingUsers, setInvitingUsers] = useState<Set<string>>(new Set());
  const [invitedUsers, setInvitedUsers] = useState<Set<string>>(new Set());

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setMemberCode('');
      setSearchResults([]);
      setFoundMember(null);
      setHasSearched(false);
      setInvitingUsers(new Set());
      setInvitedUsers(new Set());
    }
  }, [isOpen]);

  // Search users when debounced term changes
  const performSearch = useCallback(async () => {
    if (!debouncedSearchTerm.trim()) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);
    try {
      const result = await searchUsers(debouncedSearchTerm, gymId);
      if (result.success) {
        setSearchResults(result.data || []);
        setHasSearched(true);
      } else {
        toast.error(result.error || 'Arama sırasında hata oluştu');
        setSearchResults([]);
        setHasSearched(true);
      }
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Arama sırasında hata oluştu');
      setSearchResults([]);
      setHasSearched(true);
    } finally {
      setIsSearching(false);
    }
  }, [debouncedSearchTerm, gymId]);

  useEffect(() => {
    performSearch();
  }, [performSearch]);

  // Handle member search by code
  const handleMemberSearch = async () => {
    const code = memberCode.trim();
    if (!code) {
      toast.error('Lütfen üye kodunu girin');
      return;
    }

    setIsSearchingMember(true);
    try {
      const result = await findMemberByCode(code);

      if (result.success && result.data) {
        setFoundMember(result.data);
        toast.success('Üye bulundu!');
      } else {
        setFoundMember(null);
        toast.error('Bu kodla üye bulunamadı');
      }
    } catch (error) {
      toast.error('Arama sırasında bir hata oluştu');
      setFoundMember(null);
    } finally {
      setIsSearchingMember(false);
    }
  };

  // Handle invite user
  const handleInviteUser = async (userId: string, userName: string) => {
    setInvitingUsers(prev => new Set(prev).add(userId));

    try {
      const result = await sendGymMemberInvite(gymId, userId);

      if (result.success) {
        setInvitedUsers(prev => new Set(prev).add(userId));
        toast.success(`${userName} başarıyla davet edildi!`);
        onMemberInvited?.();
      } else {
        toast.error(result.error || 'Davet gönderilirken hata oluştu');
      }
    } catch (error) {
      console.error('Invite error:', error);
      toast.error('Davet gönderilirken hata oluştu');
    } finally {
      setInvitingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  // Handle invite member by code
  const handleInviteMemberByCode = async () => {
    if (!foundMember) return;

    setInvitingUsers(prev => new Set(prev).add(foundMember.profile_id));

    try {
      const result = await sendGymMemberInvite(gymId, foundMember.profile_id);

      if (result.success) {
        toast.success(`${foundMember.profiles.full_name} başarıyla davet edildi!`);
        onMemberInvited?.();
        setFoundMember(null);
        setMemberCode('');
      } else {
        toast.error(result.error || 'Davet gönderilirken hata oluştu');
      }
    } catch (error) {
      console.error('Invite error:', error);
      toast.error('Davet gönderilirken hata oluştu');
    } finally {
      setInvitingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(foundMember.profile_id);
        return newSet;
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Üye Davet Et
          </DialogTitle>
          <DialogDescription>
            Kullanıcı adı, e-posta ile arama yaparak veya üye kodu ile salon üyesi davet edin
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="search" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Ad/E-posta ile Ara
            </TabsTrigger>
            <TabsTrigger value="code" className="flex items-center gap-2">
              <QrCode className="h-4 w-4" />
              Üye Kodu ile Ara
            </TabsTrigger>
          </TabsList>

          <TabsContent value="search" className="space-y-6">
            {/* Search Input */}
            <div className="relative">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Kullanıcı adı veya e-posta ile ara..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10"
              />
              {isSearching && (
                <Loader2 className="text-muted-foreground absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2 animate-spin" />
              )}
            </div>

            {/* Search Results */}
            <div className="max-h-96 space-y-4 overflow-y-auto">
              {hasSearched && searchResults.length === 0 && (
                <div className="py-8 text-center">
                  <Users className="text-muted-foreground mx-auto h-12 w-12" />
                  <h3 className="mt-4 text-lg font-semibold">
                    Kullanıcı bulunamadı
                  </h3>
                  <p className="text-muted-foreground">
                    Arama kriterlerinize uygun kullanıcı bulunamadı. Farklı
                    terimler deneyin.
                  </p>
                </div>
              )}

              {searchResults.length > 0 && (
                <div className="space-y-3">
                  {searchResults.map(user => {
                    const isInviting = invitingUsers.has(user.id);
                    const isInvited = invitedUsers.has(user.id);

                    return (
                      <div
                        key={user.id}
                        className="flex items-center justify-between rounded-lg border p-4"
                      >
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarFallback>
                              {user.full_name
                                .split(' ')
                                .map(n => n[0])
                                .join('')
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{user.full_name}</div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {isInvited ? (
                            <div className="flex items-center gap-2 text-green-600">
                              <Check className="h-4 w-4" />
                              <span className="text-sm font-medium">
                                Davet Edildi
                              </span>
                            </div>
                          ) : (
                            <Button
                              size="sm"
                              onClick={() =>
                                handleInviteUser(user.id, user.full_name)
                              }
                              disabled={isInviting}
                            >
                              {isInviting ? (
                                <>
                                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                                  Davet Ediliyor...
                                </>
                              ) : (
                                <>
                                  <UserPlus className="mr-2 h-3 w-3" />
                                  Davet Et
                                </>
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="code" className="space-y-6">
            {/* Member Code Search */}
            <div className="space-y-4">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <QrCode className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                  <Input
                    placeholder="Üye kodunu girin (örn: **********)"
                    value={memberCode}
                    onChange={e => setMemberCode(e.target.value.toUpperCase())}
                    className="pl-10"
                    onKeyDown={e => e.key === 'Enter' && handleMemberSearch()}
                  />
                </div>
                <Button
                  onClick={handleMemberSearch}
                  disabled={isSearchingMember || !memberCode.trim()}
                >
                  {isSearchingMember ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Member Search Result */}
              {foundMember && (
                <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50">
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">
                            {foundMember.profiles?.full_name || 'Bilinmeyen Kullanıcı'}
                          </h3>
                          <div className="text-muted-foreground flex items-center gap-2 text-sm">
                            <Mail className="h-3 w-3" />
                            {foundMember.profiles?.email || 'Email bulunamadı'}
                          </div>
                          {foundMember.age && (
                            <div className="text-muted-foreground text-sm">
                              Yaş: {foundMember.age}
                            </div>
                          )}
                        </div>
                        <Badge variant="secondary">
                          {foundMember.invite_code}
                        </Badge>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          onClick={handleInviteMemberByCode}
                          disabled={invitingUsers.has(foundMember.profile_id)}
                        >
                          {invitingUsers.has(foundMember.profile_id) ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Davet Ediliyor...
                            </>
                          ) : (
                            <>
                              <UserPlus className="mr-2 h-4 w-4" />
                              Davet Et
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
