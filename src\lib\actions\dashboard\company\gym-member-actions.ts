'use server';

import {
  createAction,
  createAdminAction,
} from '../../core/core';
import { GymMembershipPackages, GymPackages } from '@/types/database/tables';
import { ApiResponse } from '@/types/global/api';
import { revalidatePath } from 'next/cache';
import { MembershipWithMember } from '@/types/business/membership';
export interface MembershipPackageAssignment {
  id: string;
  membershipId: string;
  packageId: string;
  packageName: string;
  packageType: string;
  durationDays: number;
  startDate: string;
  endDate: string | null;
  purchasePrice: number;
  status: string;
}

/**
 * Assign package to member
 */
export async function assignPackageToMember(
  membershipId: string,
  packageId: string,
  startDate?: string
): Promise<ApiResponse<MembershipPackageAssignment>> {
  return await createAdminAction<MembershipPackageAssignment>(
    async (_, _supabase, _userId, adminClient) => {
      // Membership bilgilerini al
      const { data: membership, error: membershipError } = await adminClient
        .from('gym_memberships')
        .select('id, profile_id, gym_id')
        .eq('id', membershipId)
        .single();

      if (membershipError || !membership) {
        throw new Error('Üyelik bilgisi bulunamadı.');
      }

      // Package bilgilerini al
      const { data: packageData, error: packageError } = await adminClient
        .from('gym_packages')
        .select('id, name, package_type, duration_days, price, session_count, session_duration_minutes, max_participants')
        .eq('id', packageId)
        .eq('gym_id', membership.gym_id || '')
        .eq('is_active', true)
        .single();

      if (packageError || !packageData) {
        throw new Error('Paket bilgisi bulunamadı veya paket aktif değil.');
      }

      // Tarih hesaplamaları
      const packageStartDate = startDate ? new Date(startDate) : new Date();
      let packageEndDate: Date | null = null;

      if (packageData.duration_days) {
        packageEndDate = new Date(packageStartDate);
        packageEndDate.setDate(
          packageEndDate.getDate() + packageData.duration_days
        );
      }

      // gym_id null kontrolü
      if (!membership.gym_id) {
        throw new Error('Üyelik salon bilgisi eksik.');
      }

      // Session bilgilerini hesapla
      const totalSessions = packageData.session_count || 0;
      const remainingSessions = totalSessions;
      const usedSessions = 0;

      // Package assignment oluştur
      const { data: assignment, error: assignmentError } = await adminClient
        .from('gym_membership_packages')
        .insert({
          membership_id: membershipId,
          gym_package_id: packageId,
          gym_id: membership.gym_id,
          start_date: packageStartDate.toISOString(),
          end_date: packageEndDate?.toISOString() || null,
          purchase_price: packageData.price,
          status: 'active',
          total_sessions: totalSessions,
          remaining_sessions: remainingSessions,
          used_sessions: usedSessions,
        })
        .select('id, start_date, end_date, purchase_price, status, total_sessions, remaining_sessions, used_sessions')
        .single();

      if (assignmentError) {
        throw new Error(
          `Paket ataması yapılırken hata: ${assignmentError.message}`
        );
      }

      // Response formatını düzenle
      const result: MembershipPackageAssignment = {
        id: assignment.id,
        membershipId: membershipId,
        packageId: packageId,
        packageName: packageData.name,
        packageType: packageData.package_type || '',
        durationDays: packageData.duration_days,
        startDate: assignment.start_date,
        endDate: assignment.end_date,
        purchasePrice: assignment.purchase_price,
        status: assignment.status,
      };

      // Cache'i güncelle
      revalidatePath(`/dashboard/gym/${membership.gym_id}/members/${membershipId}`);
      revalidatePath(`/dashboard/gym/${membership.gym_id}/members`);

      return result;
    }
  );
}

// GymMemberWithDetails type is now imported from @/types
/**
 * Salon üyelerini getirir - Tüm yetkili roller için (RLS ile kontrol)
 * Manager, Gym Manager ve Trainer kullanabilir
 */
export async function getGymMembers(
  gymId: string,
  page: number = 1,
  limit: number = 20
): Promise<ApiResponse<{ members: MembershipWithMember[]; totalCount: number }>> {
  return createAction(
    async (_, supabase, _userId) => {
      const offset = (page - 1) * limit;

      const {
        data: memberships,
        error: membershipsError,
        count,
      } = await supabase
        .from('gym_memberships')
        .select(
          `
            *,
            member:profiles!gym_memberships_profile_id_fkey
(*)
          `,
          { count: 'exact' }
        )
        .eq('gym_id', gymId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (membershipsError) {
        throw new Error(`Üyeler getirilirken hata: ${membershipsError.message}`);
      }

      return {
        members: memberships || [],
        totalCount: count || 0,
      };
    }
  );
}

/**
 * Belirli bir üyenin aktif paketlerini getirir
 */
export async function getMemberPackages(
  gymId: string,
  memberId: string
): Promise<
  ApiResponse<(GymMembershipPackages & { gym_package?: GymPackages })[]>
> {
  return createAction(
    async (_, supabase, _userId) => {

      // Önce üyenin membership'ini bul
      const { data: membership, error: membershipError } = await supabase
        .from('gym_memberships')
        .select('id')
        .eq('gym_id', gymId)
        .eq('profile_id', memberId)
        .eq('status', 'active')
        .single();

      if (membershipError || !membership) {
        throw new Error('Üye bu salonda bulunamadı');
      }

      // Şimdi bu membership'e ait paketleri getir
      const { data, error } = await supabase
        .from('gym_membership_packages')
        .select(
          `
          *,
          gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
            name,
            package_type,
            max_participants,
            session_count,
            session_duration_minutes
          )
        `
        )
        .eq('membership_id', membership.id)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Üye paketleri getirilemedi: ${error.message}`);
      }

      const packages = data || [];

      // View kullanmak yerine manuel olarak kalan seans sayısını hesapla
      const packagesWithStats = await Promise.all(
        packages.map(async (pkg: any) => {
          // Bu pakete ait randevu katılımlarını say
          const { data: participantData, error: participantError } = await supabase
            .from('appointment_participants')
            .select(`
              id,
              status,
              appointment:appointments!appointment_participants_appointment_id_fkey(
                id,
                status
              )
            `)
            .eq('gym_membership_package_id', pkg.id);

          if (participantError) {
            console.error('Participant data error:', participantError);
            // Hata durumunda varsayılan değerleri kullan
            return {
              ...pkg,
              computed_remaining_sessions: pkg.remaining_sessions ?? pkg.total_sessions ?? 0,
            };
          }

          // Tamamlanan ve planlanan seansları say
          const participants = participantData || [];
          const completedCount = participants.filter((p: any) =>
            p.appointment?.status === 'completed' &&
            p.status !== 'cancelled' &&
            p.status !== 'canceled'
          ).length;

          const plannedCount = participants.filter((p: any) =>
            p.appointment?.status === 'scheduled' &&
            (p.status === 'confirmed' || !p.status)
          ).length;

          // Kalan seans sayısını hesapla
          const totalSessions = pkg.total_sessions ?? 0;
          const usedSessions = completedCount + plannedCount;
          const computedRemaining = Math.max(totalSessions - usedSessions, 0);

          return {
            ...pkg,
            computed_remaining_sessions: computedRemaining,
            planned_sessions_count: plannedCount,
            completed_sessions_count: completedCount,
          };
        })
      );

      // Sadece kalan seansı olan paketleri filtrele
      const filtered = packagesWithStats.filter(
        (p: any) => (p.computed_remaining_sessions ?? 0) > 0
      );

      return filtered;
    }
  );
}

/**
 * Salona yeni üye ekler - Tüm yetkili roller için (RLS ile kontrol)
 * Manager, Gym Manager ve Trainer kullanabilir
 */
export async function addMemberToGym(
  gymId: string,
  memberData: {
    profile_id: string;
  }
): Promise<ApiResponse<any>> {
  return createAction(
    async (_, supabase, _userId) => {
      // Check if member already exists in this gym
      const { data: existingMember } = await supabase
        .from('gym_memberships')
        .select('id')
        .eq('gym_id', gymId)
        .eq('profile_id', memberData.profile_id)
        .eq('status', 'active')
        .single();

      if (existingMember) {
        throw new Error('Bu kullanıcı zaten bu salonda aktif üye');
      }

      // Add new member
      const { data: newMember, error } = await supabase
        .from('gym_memberships')
        .insert({
          gym_id: gymId,
          profile_id: memberData.profile_id,
          status: 'active',
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Üye eklenirken hata: ${error.message}`);
      }

      return newMember;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/members`],
    }
  );
}

/**
 * Üye bilgilerini günceller - Tüm yetkili roller için (RLS ile kontrol)
 * Manager, Gym Manager ve Trainer kullanabilir
 */
export async function updateGymMember(
  gymId: string,
  memberId: string,
  updateData: {
    package_id?: string;
    end_date?: string;
    notes?: string;
    status?: 'active' | 'inactive' | 'suspended';
  }
): Promise<ApiResponse<any>> {
  return createAction(
    async (_, supabase, userId) => {
      // Update member
      const { data: updatedMember, error } = await supabase
        .from('gym_memberships')
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
          updated_by: userId,
        })
        .eq('id', memberId)
        .eq('gym_id', gymId) // Ensure member belongs to this gym
        .select()
        .single();

      if (error) {
        throw new Error(`Üye güncellenirken hata: ${error.message}`);
      }

      if (!updatedMember) {
        throw new Error('Üye bulunamadı veya bu salona ait değil');
      }

      return updatedMember;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/members`],
    }
  );
}

/**
 * Üye durumunu güncelleme - Tüm yetkili roller için (RLS ile kontrol)
 */
export async function updateMemberStatus(
  gymId: string,
  data: {
    membershipId: string;
    status: 'active' | 'passive';
  }
): Promise<ApiResponse<void>> {
  return createAction(
    async (_, supabase, _userId) => {
      const { error } = await supabase
        .from('gym_memberships')
        .update({
          status: data.status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', data.membershipId)
        .eq('gym_id', gymId); 

      if (error) {
        throw new Error(
          'Üye durumu güncellenirken hata oluştu: ' + error.message
        );
      }

      return undefined;
    },
    {
      revalidatePaths: [`/dashboard/gym/${gymId}/members`],
    }
  );
}

// Yeni tipler
export interface MemberProfileData {
  id: string;
  full_name: string;
  email: string | null;
  avatar_url: string | null;
  phone_number: string | null;
  is_guest_account: boolean;
}

export interface MemberDetailsData {
  age?: number;
  gender?: string;
  height_cm?: number;
  weight_kg?: number;
  fitness_goal?: string;
}

export interface MemberPackageData {
  id: string;
  name: string;
  package_type: string | null;
  start_date: string;
  end_date: string | null;
  purchase_price: number;
  status: string;
  duration_days: number | null;
}

export interface CompleteMemberData {
  profile: MemberProfileData;
  memberDetails: MemberDetailsData | null;
  packages: MemberPackageData[];
}

/**
 * Membership ID ile üyenin tüm bilgilerini getirir (manager yetkisi ile)
 * Profiles, member_details ve paket bilgilerini paralel olarak çeker
 */
export async function getMemberCompleteData(
  membershipId: string,
  gymId: string
): Promise<ApiResponse<CompleteMemberData>> {
  return createAction(
    async (_, supabase, _userId) => {
      // Önce membership bilgisini al ve gym kontrolü yap
      const { data: membership, error: membershipError } = await supabase
        .from('gym_memberships')
        .select('profile_id, gym_id')
        .eq('id', membershipId)
        .eq('gym_id', gymId) // Gym sahipliği kontrolü
        .single();

    if (membershipError || !membership) {
      throw new Error('Üyelik bilgisi bulunamadı veya bu salona ait değil.');
    }

    const memberId = membership.profile_id;

    // Paralel olarak tüm verileri çek
    const [profileResult, memberDetailsResult, packagesResult] =
      await Promise.all([
        // Profil bilgilerini çek
        supabase
          .from('profiles')
          .select(
            'id, full_name, email, avatar_url, phone_number, is_guest_account'
          )
          .eq('id', memberId)
          .single(),

        // Member details bilgilerini çek
        supabase
          .from('member_details')
          .select('age, gender, height_cm, weight_kg, fitness_goal')
          .eq('profile_id', memberId)
          .maybeSingle(),

        // Paket bilgilerini çek
        supabase
          .from('gym_membership_packages')
          .select(
            `
            id,
            start_date,
            end_date,
            purchase_price,
            status,
            gym_packages!inner(
              name,
              package_type,
              duration_days
            )
          `
          )
          .eq('membership_id', membershipId)
          .order('created_at', { ascending: false }),
      ]);

    // Profil bilgisi kontrolü
    if (profileResult.error || !profileResult.data) {
      throw new Error('Üye profil bilgileri alınamadı.');
    }

    // Paket verilerini formatla
    const packages: MemberPackageData[] = (packagesResult.data || []).map(
      (pkg: any) => {
        const gymPackage = Array.isArray(pkg.gym_packages)
          ? pkg.gym_packages[0]
          : pkg.gym_packages;

        return {
          id: pkg.id,
          name: gymPackage?.name || 'Bilinmeyen Paket',
          package_type: gymPackage?.package_type || null,
          start_date: pkg.start_date,
          end_date: pkg.end_date,
          purchase_price: pkg.purchase_price,
          status: pkg.status,
          duration_days: gymPackage?.duration_days || null,
        };
      }
    );

    const result: CompleteMemberData = {
      profile: profileResult.data,
      memberDetails: memberDetailsResult.data || null,
      packages,
    };

    return result;
    }
  );
}
